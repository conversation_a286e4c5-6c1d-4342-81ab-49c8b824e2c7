from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    # Databáze
    DATABASE_URL: str = "sqlite:///./ai_korektor.db"

    # OpenAI
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-4o-mini"

    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False

    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # Zpracování
    TEMP_DIR: str = "./temp"
    MAX_VIDEO_DURATION: int = 10800  # 3 hodiny v sekundách
    WHISPER_MODEL: str = "whisper-1"
    GPT_MODEL: str = "gpt-4o-mini"
    CONFIDENCE_THRESHOLD: float = 0.9

    # Logging
    LOG_LEVEL: str = "INFO"

    model_config = {"env_file": ".env", "case_sensitive": True}


settings = Settings()