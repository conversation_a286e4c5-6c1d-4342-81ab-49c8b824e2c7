"""YouTube service for downloading videos and extracting subtitles."""

import yt_dlp
from typing import Dict, List, Any
import re


class YouTubeService:
    """Service for handling YouTube video operations."""

    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """Validuje YouTube URL"""
        youtube_patterns = [
            r'youtube\.com/watch\?v=',
            r'youtu\.be/',
            r'youtube\.com/embed/',
            r'youtube\.com/v/'
        ]
        return any(re.search(pattern, url) for pattern in youtube_patterns)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """Získá informace o videu"""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                "title": info.get("title"),
                "duration": info.get("duration"),
                "description": info.get("description"),
                "uploader": info.get("uploader")
            }

    def extract_subtitles(self, url: str) -> List[Dict[str, Any]]:
        """Extrahuje titulky z videa"""
        import tempfile
        import os
        import re

        with tempfile.TemporaryDirectory() as temp_dir:
            ydl_opts = {
                'writesubtitles': True,
                'writeautomaticsub': True,
                'subtitleslangs': ['cs', 'cs-CZ', 'en'],
                'skip_download': True,
                'quiet': True,
                'outtmpl': os.path.join(temp_dir, '%(title)s.%(ext)s'),
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)

                # Najdi stažené titulky
                subtitle_files = []
                for file in os.listdir(temp_dir):
                    if file.endswith(('.vtt', '.srt')):
                        subtitle_files.append(os.path.join(temp_dir, file))

                if not subtitle_files:
                    return []

                # Parsuj první nalezený soubor titulků
                subtitle_file = subtitle_files[0]
                return self._parse_subtitle_file(subtitle_file)

    def _parse_subtitle_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Parsuje soubor titulků do strukturovaného formátu"""
        import re

        segments = []

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if file_path.endswith('.vtt'):
            # WebVTT formát
            pattern = r'(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})\n(.*?)(?=\n\n|\n\d{2}:|\Z)'
            matches = re.findall(pattern, content, re.DOTALL)

            for i, (start, end, text) in enumerate(matches):
                start_ms = self._time_to_ms(start)
                end_ms = self._time_to_ms(end)
                clean_text = re.sub(r'<[^>]+>', '', text).strip()

                if clean_text:
                    segments.append({
                        "start_time_ms": start_ms,
                        "end_time_ms": end_ms,
                        "text": clean_text,
                        "sequence": i + 1
                    })

        return segments

    def _time_to_ms(self, time_str: str) -> int:
        """Převede čas ve formátu HH:MM:SS.mmm na milisekundy"""
        parts = time_str.split(':')
        hours = int(parts[0])
        minutes = int(parts[1])
        seconds_parts = parts[2].split('.')
        seconds = int(seconds_parts[0])
        milliseconds = int(seconds_parts[1])

        return (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds

    def download_audio(self, url: str, output_dir: str, project_id: str, progress_callback=None) -> str:
        """Stáhne audio z YouTube videa s progress tracking"""
        import os
        from pathlib import Path

        try:
            # Vytvoř výstupní adresář pokud neexistuje
            Path(output_dir).mkdir(parents=True, exist_ok=True)

            # Definuj výstupní cestu
            output_template = os.path.join(output_dir, f"{project_id}_%(title)s.%(ext)s")

            def progress_hook(d):
                if progress_callback and d['status'] == 'downloading':
                    if 'total_bytes' in d:
                        percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                        progress_callback(f"Stahování audio: {percent:.1f}%")
                elif progress_callback and d['status'] == 'finished':
                    progress_callback("Audio staženo, zpracovává se...")

            ydl_opts = {
                'format': 'bestaudio/best',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'outtmpl': output_template,
                'quiet': True,
                'no_warnings': True,
                'progress_hooks': [progress_hook] if progress_callback else [],
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Nejdříve získej info o videu
                info = ydl.extract_info(url, download=False)
                video_title = info.get('title', 'unknown')
                duration = info.get('duration', 0)

                # Zkontroluj délku videa (max 3 hodiny podle PRD)
                if duration > 10800:  # 3 hodiny
                    raise ValueError(f"Video je příliš dlouhé ({duration}s). Maximum je 3 hodiny.")

                # Stáhni audio
                ydl.download([url])

                # Najdi stažený soubor
                expected_filename = f"{project_id}_{video_title}.mp3"
                # Očisti název souboru od problematických znaků
                import re
                safe_filename = re.sub(r'[<>:"/\\|?*]', '_', expected_filename)
                audio_file_path = os.path.join(output_dir, safe_filename)

                # Najdi skutečný soubor (yt-dlp může změnit název)
                for file in os.listdir(output_dir):
                    if file.startswith(project_id) and file.endswith('.mp3'):
                        actual_path = os.path.join(output_dir, file)
                        if actual_path != audio_file_path:
                            os.rename(actual_path, audio_file_path)
                        break
                else:
                    raise FileNotFoundError("Stažený audio soubor nebyl nalezen")

                if progress_callback:
                    progress_callback("Audio úspěšně staženo")

                return audio_file_path

        except yt_dlp.DownloadError as e:
            raise ValueError(f"Chyba při stahování z YouTube: {str(e)}")
        except Exception as e:
            raise ValueError(f"Neočekávaná chyba při stahování audio: {str(e)}")

    def cleanup_temp_files(self, file_paths: List[str]) -> None:
        """Vyčistí dočasné soubory"""
        import os
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                # Log warning ale nepřerušuj proces
                print(f"Warning: Nepodařilo se smazat dočasný soubor {file_path}: {e}")

    def get_video_duration(self, url: str) -> int:
        """Získá délku videa v sekundách"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                return info.get('duration', 0)
        except Exception:
            return 0
