"""Project management endpoints for AI Korektor Titulků."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas import CreateProjectRequest, Project, ProjectSummary, UpdateSegmentRequest, SubtitleSegment
from app.services.project_service import ProjectService
from app.services.youtube_service import YouTubeService

router = APIRouter(prefix="/projects", tags=["projects"])


@router.post("/", response_model=Project)
async def create_project(
    project_data: CreateProjectRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Vytvoří nový projekt a spustí zpracování na pozadí
    """
    # Validace YouTube URL
    if not YouTubeService.is_valid_youtube_url(project_data.youtube_url):
        raise HTTPException(status_code=400, detail="Invalid YouTube URL")

    # Vytvoř projekt v databázi
    project_service = ProjectService(db)
    project = project_service.create_project(project_data.youtube_url)

    # Pro testování - jednoduché zpracování bez background
    try:
        # Vytvoř mock segmenty pro testování
        from app.models import SubtitleSegment as SegmentModel
        import uuid

        # Přidej testovací segmenty
        test_segments = [
            {
                'start_time': 0.0,
                'end_time': 3.0,
                'original_text': 'Toto je první testovací segment.',
                'corrected_text': 'Toto je první testovací segment.'
            },
            {
                'start_time': 3.0,
                'end_time': 6.0,
                'original_text': 'Druhý testovací segment s chybou.',
                'corrected_text': 'Druhý testovací segment bez chyby.'
            }
        ]

        for i, segment in enumerate(test_segments):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project.project_id,
                sequence_number=i + 1,
                start_time_ms=int(segment['start_time'] * 1000),
                end_time_ms=int(segment['end_time'] * 1000),
                original_text=segment['original_text'],
                corrected_text=segment['corrected_text'],
                status="auto_corrected" if segment['corrected_text'] != segment['original_text'] else "unchanged"
            )
            db.add(segment_model)

        # Aktualizuj projekt status
        project.status = "needs_review"
        project.video_title = "Testovací video"
        project.video_duration = 6
        db.commit()

    except Exception as e:
        project.status = "error"
        project.error_message = str(e)
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při zpracování: {str(e)}")

    return project


@router.get("/", response_model=List[ProjectSummary])
async def get_projects(db: Session = Depends(get_db)):
    """
    Vrátí seznam všech projektů bez detailů segmentů
    """
    project_service = ProjectService(db)
    return project_service.get_all_projects()

@router.get("/{project_id}", response_model=Project)
async def get_project(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí detailní informace o projektu včetně segmentů
    """
    project_service = ProjectService(db)
    project = project_service.get_project_by_id(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project


@router.put("/{project_id}/segments/{segment_id}", response_model=SubtitleSegment)
async def update_segment(
    project_id: str,
    segment_id: str,
    update_data: UpdateSegmentRequest,
    db: Session = Depends(get_db)
):
    """
    Aktualizuje text segmentu (uživatelská editace)
    """
    project_service = ProjectService(db)
    segment = project_service.update_segment_text(segment_id, update_data.corrected_text)
    if not segment:
        raise HTTPException(status_code=404, detail="Segment not found")
    return segment

@router.post("/{project_id}/segments/{segment_id}/suggestions/{suggestion_id}/apply")
async def apply_suggestion(
    project_id: str,
    segment_id: str,
    suggestion_id: str,
    db: Session = Depends(get_db)
):
    """
    Aplikuje konkrétní návrh korekce
    """
    project_service = ProjectService(db)
    success = project_service.apply_correction_suggestion(suggestion_id)
    if not success:
        raise HTTPException(status_code=404, detail="Suggestion not found")
    return {"success": True}

@router.delete("/{project_id}")
async def delete_project(project_id: str, db: Session = Depends(get_db)):
    """
    Smaže projekt a všechna související data
    """
    project_service = ProjectService(db)
    success = project_service.delete_project(project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"message": "Project deleted successfully"}


@router.get("/{project_id}/export/preview")
async def preview_export(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí preview SRT exportu s validací a statistikami
    """
    project_service = ProjectService(db)
    export_data = project_service.generate_srt_file(project_id, include_preview=True)
    if not export_data:
        raise HTTPException(status_code=404, detail="Project not found or no segments")

    return export_data

@router.post("/{project_id}/export")
async def export_project(project_id: str, db: Session = Depends(get_db)):
    """
    Exportuje projekt do SRT formátu
    """
    from fastapi.responses import Response

    project_service = ProjectService(db)
    export_data = project_service.generate_srt_file(project_id)
    if not export_data:
        raise HTTPException(status_code=404, detail="Project not found or no segments")

    return Response(
        content=export_data["content"],
        media_type="text/plain",
        headers={"Content-Disposition": f"attachment; filename={export_data['filename']}"}
    )