import React, { useState, useEffect, useRef } from 'react';
import ReactPlayer from 'react-player';
import { useProjectContext } from '../contexts/ProjectContext';
import SubtitleSegmentRow from './SubtitleSegmentRow';
import { Play, Pause, AlertCircle, Loader2 } from 'lucide-react';

const EditorPanel: React.FC = () => {
  const { state, updateSegment } = useProjectContext();
  const [activeSegmentId, setActiveSegmentId] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [userClickedSegment, setUserClickedSegment] = useState(false);
  const playerRef = useRef<ReactPlayer>(null);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!state.currentProject || !activeSegmentId) return;

      const activeIndex = state.currentProject.segments.findIndex(
        (s) => s.segment_id === activeSegmentId
      );

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          if (activeIndex < state.currentProject.segments.length - 1) {
            setActiveSegmentId(
              state.currentProject.segments[activeIndex + 1].segment_id
            );
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          if (activeIndex > 0) {
            setActiveSegmentId(
              state.currentProject.segments[activeIndex - 1].segment_id
            );
          }
          break;
        case ' ':
          e.preventDefault();
          setIsPlaying(!isPlaying);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.currentProject, activeSegmentId, isPlaying]);

  // Sync player with active segment (only when user clicks)
  useEffect(() => {
    if (activeSegmentId && state.currentProject && playerRef.current && userClickedSegment) {
      const segment = state.currentProject.segments.find(
        (s) => s.segment_id === activeSegmentId
      );
      if (segment) {
        playerRef.current.seekTo(segment.start_time_ms / 1000);
        // Reset flag after seeking
        setTimeout(() => setUserClickedSegment(false), 100);
      }
    }
  }, [activeSegmentId, state.currentProject, userClickedSegment]);

  // Update active segment based on player time (only when not user-initiated)
  useEffect(() => {
    if (state.currentProject && currentTime > 0 && !userClickedSegment) {
      const currentTimeMs = currentTime * 1000;
      const currentSegment = state.currentProject.segments.find(
        (s) => s.start_time_ms <= currentTimeMs && s.end_time_ms >= currentTimeMs
      );

      if (currentSegment && currentSegment.segment_id !== activeSegmentId) {
        setActiveSegmentId(currentSegment.segment_id);
      }
    }
  }, [currentTime, state.currentProject, activeSegmentId, userClickedSegment]);

  const handleSegmentClick = (segmentId: string) => {
    setUserClickedSegment(true);
    setActiveSegmentId(segmentId);
  };

  const handleTextChange = async (segmentId: string, newText: string) => {
    try {
      await updateSegment(segmentId, newText);
    } catch (error) {
      console.error('Failed to update segment:', error);
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!state.currentProject) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Vyberte projekt pro úpravu
          </h2>
          <p className="text-gray-600">
            Klikněte na projekt v levém panelu nebo vytvořte nový.
          </p>
        </div>
      </div>
    );
  }

  if (state.currentProject.status === 'processing') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Zpracovává se...
          </h2>
          <p className="text-gray-600">
            Video se zpracovává pomocí AI. Počkejte prosím.
          </p>
        </div>
      </div>
    );
  }

  if (state.currentProject.status === 'error') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Chyba při zpracování
          </h2>
          <p className="text-gray-600 mb-4">
            {state.currentProject.error_message || 'Neočekávaná chyba'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Zkusit znovu
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Video Player */}
      <div className="bg-black flex-shrink-0">
        <ReactPlayer
          ref={playerRef}
          url={state.currentProject.youtube_url}
          width="100%"
          height="300px"
          controls={true}
          playing={isPlaying}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onProgress={({ playedSeconds }) => setCurrentTime(playedSeconds)}
          config={{
            youtube: {
              playerVars: {
                modestbranding: 1,
                rel: 0,
              },
            },
          }}
        />
      </div>

      {/* Segments Header */}
      <div className="border-b border-gray-200 bg-white px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Titulky ({state.currentProject.segments.length} segmentů)
            </h3>
            <p className="text-sm text-gray-600">
              {state.currentProject.video_title}
            </p>
          </div>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-orange-600 font-medium">
                {state.currentProject.segments?.filter(s => s.status === 'NEEDS_REVIEW').length || 0}
              </span>
              <span className="text-gray-600">k revizi</span>
            </div>
          </div>
        </div>
      </div>

      {/* Segments List - Fixed height with scrollbar */}
      <div className="flex-1 min-h-0 overflow-y-auto bg-white">
        <div className="divide-y divide-gray-100">
          {state.currentProject?.segments.map((segment) => (
            <SubtitleSegmentRow
              key={segment.segment_id}
              segment={segment}
              isActive={activeSegmentId === segment.segment_id}
              onSegmentClick={handleSegmentClick}
              onTextChange={handleTextChange}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default EditorPanel;